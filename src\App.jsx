import { useState } from "react";
import { questions } from "./data/questions";
import IntroScreen from "./components/IntroScreen";
import InfoForm from "./components/InfoForm";
import QuestionCard from "./components/QuestionCard";
import ResultScreen from "./components/ResultScreen";

export default function App() {
  const [screen, setScreen] = useState("intro");
  const [user, setUser] = useState({});
  const [index, setIndex] = useState(0);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(40); // ⏱️

  // ⏳ Countdown effect
  useEffect(() => {
    if (screen === "quiz" && timeLeft > 0) {
      const timer = setInterval(() => setTimeLeft((t) => t - 1), 1000);
      return () => clearInterval(timer);
    }
    if (screen === "quiz" && timeLeft === 0) {
      setScreen("result");
    }
  }, [screen, timeLeft]);

  const handleSwipe = (swipedSecure) => {
    if (swipedSecure === questions[index].isSecure) {
      setScore((s) => s + 1);
    }
    if (index + 1 < questions.length) {
      setIndex((i) => i + 1);
    } else {
      setScreen("result");
    }
  };

  const resetGame = () => {
    setScore(0);
    setIndex(0);
    setTimeLeft(40);
    setScreen("intro");
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-100 to-white flex items-center justify-center">
      {screen === "intro" && <IntroScreen onStart={() => setScreen("form")} />}
      {screen === "form" && (
        <InfoForm
          onSubmit={(data) => {
            setUser(data);
            setScreen("quiz");
          }}
        />
      )}
      {screen === "quiz" && (
        <div className="w-full max-w-md">
          <div className="text-center text-gray-700 font-semibold">
            ⏱ Time Left: {timeLeft}s
          </div>
          <QuestionCard question={questions[index]} onSwipe={handleSwipe} />
        </div>
      )}
      {screen === "result" && (
        <ResultScreen score={score} onRetry={resetGame} />
      )}
    </div>
  );
}
