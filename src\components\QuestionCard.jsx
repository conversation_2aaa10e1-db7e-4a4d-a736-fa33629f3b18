import React, { useState } from "react";
import { useSwipeable } from "react-swipeable";

export default function QuestionCard({ question, onSwipe }) {
  const [animation, setAnimation] = useState("");

  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => triggerSwipe(false),
    onSwipedRight: () => triggerSwipe(true),
    trackMouse: true,
  });

  const triggerSwipe = (isRight) => {
    setAnimation(isRight ? "animate-swipe-right" : "animate-swipe-left");
    setTimeout(() => {
      setAnimation("");
      onSwipe(isRight);
    }, 400);
  };

  return (
    <div {...swipeHandlers} className={`p-6 text-center ${animation}`}>
      <h2 className="text-xl font-semibold mb-4">
        Question {question.id} of 10
      </h2>
      <div className="bg-white rounded-xl shadow-lg p-6 text-2xl font-bold border-4 border-blue-500 mx-4">
        {question.text}
      </div>

      <div className="flex justify-center mt-6 gap-8">
        <button
          onClick={() => triggerSwipe(false)}
          className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg shadow-md"
        >
          Swipe Left
        </button>
        <button
          onClick={() => triggerSwipe(true)}
          className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg shadow-md"
        >
          Swipe Right
        </button>
      </div>
    </div>
  );
}
