// tailwind.config.js
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      animation: {
        "fade-in": "fadeIn 0.5s ease-out",
        "swipe-left": "swipeLeft 0.4s ease-out forwards",
        "swipe-right": "swipeRight 0.4s ease-out forwards",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: 0 },
          "100%": { opacity: 1 },
        },
        swipeLeft: {
          to: { transform: "translateX(-100%)", opacity: 0 },
        },
        swipeRight: {
          to: { transform: "translateX(100%)", opacity: 0 },
        },
      },
    },
  },

  plugins: [require("tailwindcss-animate")],
};
