import React, { useState } from "react";
export default function InfoForm({ onSubmit }) {
  const [form, setForm] = useState({
    name: "",
    email: "",
    freeCredits: false,
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit(form);
      }}
      className="p-6 space-y-4"
    >
      <input
        type="text"
        placeholder="Name"
        required
        className="w-full border p-2"
        onChange={(e) => setForm({ ...form, name: e.target.value })}
      />
      <input
        type="email"
        placeholder="Email"
        required
        className="w-full border p-2"
        onChange={(e) => setForm({ ...form, email: e.target.value })}
      />
      <label className="block">
        <input
          type="checkbox"
          onChange={(e) => setForm({ ...form, freeCredits: e.target.checked })}
        />
        <span className="ml-2">
          Check this box if you are interested in getting free credits
        </span>
      </label>
      <button className="bg-green-600 text-white px-4 py-2 rounded">
        Start Quiz
      </button>
    </form>
  );
}
